import React from "react";
import { merge } from "lodash";
import PropTypes from "prop-types";
import { Scatter } from "@ant-design/charts";

import { Empty } from "misc/empty";

import defaultChartConfig from "./config";

const ScatterChart = ({ chartConfig }) => {
    if (!chartConfig.loading && (chartConfig.data.length < 1 || chartConfig.empty?.isEmpty(chartConfig.data)))
        return <Empty title={chartConfig.empty?.title} subTitle={chartConfig.empty?.subTitle} height={chartConfig.height} />;

    return <Scatter {...merge({}, defaultChartConfig, chartConfig)} />;
};

ScatterChart.propTypes = {
    chartConfig: PropTypes.shape({
        data: PropTypes.array.isRequired
    }).isRequired
};

export default ScatterChart;
