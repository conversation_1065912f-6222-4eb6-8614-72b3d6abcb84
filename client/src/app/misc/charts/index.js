import { G2 } from "@ant-design/charts";
export { default as TimeseriesChartWrapper } from "./timeseries/TimeseriesChartWrapper";
export { default as TimeseriesLineChartElement } from "./timeseries/TimeseriesLineChartElement";
// export { default as TimeseriesBarChartElement } from "./timeseries/TimeseriesBarChartElement";
export { default as Bull<PERSON><PERSON><PERSON> } from "./BulletChart";
export { default as Dual<PERSON><PERSON><PERSON><PERSON> } from "./DualAxesChart";
export { default as Pie<PERSON><PERSON> } from "./PieChart";
export { default as Line<PERSON><PERSON> } from "./LineChart";
export { default as Bar<PERSON><PERSON> } from "./BarChart";
export { default as Column<PERSON>hart } from "./ColumnChart";
export { default as Area<PERSON>hart } from "./AreaChart";
export { default as Histo<PERSON><PERSON><PERSON> } from "./HistogramChart";
export { default as Facet<PERSON><PERSON> } from "./FacetChart";
export { default as Scatter<PERSON><PERSON> } from "./ScatterChart";

// register global hover for bar charts
G2.registerInteraction("element-link", {
    start: [
        {
            trigger: "interval:mouseenter",
            action: "element-link-by-color:link"
        }
    ],
    end: [
        {
            trigger: "interval:mouseleave",
            action: "element-link-by-color:unlink"
        }
    ]
});
